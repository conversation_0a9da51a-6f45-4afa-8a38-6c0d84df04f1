import type { FC, ReactElement } from 'react';
import React from 'react';
import { cn } from '@/lib/utils';

type TriggerButtonProps = {
  icon: ReactElement;
  topParagraph: string;
  bottomParagraph: string;
  mobileText: string;
  onClick?: () => void;
  highlighted?: boolean;
};

const SearchTriggerButton: FC<TriggerButtonProps> = ({
  icon,
  mobileText,
  bottomParagraph,
  topParagraph,
  onClick,
  highlighted = false,
}): ReactElement => {
  return (
    <div
      onClick={onClick}
      className={cn(
        'flex cursor-pointer items-center justify-center gap-1 transition-all duration-200 md:justify-start'
      )}>
      <div className={cn('hidden px-2 py-1 md:flex', highlighted && 'text-primary-500')}>{icon}</div>
      <div className="flex flex-col">
        <p
          className={cn(
            'text-black-300 block text-center text-lg md:hidden',
            highlighted && 'text-primary-500 font-bold'
          )}>
          {mobileText}
        </p>
        <p
          className={cn(
            'sm-text text-black-300 mb-1 hidden text-start font-bold md:block',
            highlighted && 'text-primary-500'
          )}>
          {topParagraph}
        </p>
        <p className={cn('sm-text text-black-100 hidden md:flex', highlighted && 'text-primary-400')}>
          {bottomParagraph}
        </p>
      </div>
    </div>
  );
};
export default SearchTriggerButton;
