'use client';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { FC, useState } from 'react';
import { Calendar as UiCalendar } from '@/components/ui/calendar';
import { DateRange } from 'react-day-picker';
import { parseDateToLocalizedDate, parseDateToString } from '@/lib/utils';
import { isSameDay } from 'date-fns';
import ColorfulBadge from '@/components/ui/colorful-badge';
import SearchAccordionWarper from './accordion-wrapper';
import { cn } from '@/lib/utils';

interface MobileDateTabsProps {
  setDate: (id: DateRange | undefined) => void;
  date: DateRange;
  isAccordionOpen: boolean;
  setIsAccordionOpen: (value: boolean) => void;
}

const disabledDays = [
  {
    from: new Date(1970, 1, 1),
    to: new Date(new Date().setDate(new Date().getDate() - 1)),
  },
];

const MobileDateTabs: FC<MobileDateTabsProps> = ({ setDate, date, isAccordionOpen, setIsAccordionOpen }) => {
  const t = useScopedI18n('homepage.search');
  const currentLocale = useCurrentLocale();
  const [activeTab, setActiveTab] = useState<'checkin' | 'checkout'>('checkin');

  const handleDateSelection = (range: DateRange | undefined) => {
    if (!range?.from) {
      return;
    }

    if (activeTab === 'checkin') {
      // Update check-in date only, preserve existing checkout
      setDate({ from: range.from, to: date?.to });
      // If we don't have checkout date, switch to checkout tab
      if (!date?.to) {
        setActiveTab('checkout');
      }
    } else if (activeTab === 'checkout') {
      // Update check-out date only, preserve existing checkin
      setDate({ from: date?.from, to: range.from });
    }
  };

  return (
    <SearchAccordionWarper
      value={`${date?.from ? parseDateToLocalizedDate(date?.from as Date, currentLocale) : ''} ${date?.to && date?.from && !isSameDay(date?.from as Date, date?.to) ? `- ${parseDateToLocalizedDate(date?.to as Date, currentLocale)}` : ''}`}
      title={t('date')}
      isAccordionOpen={isAccordionOpen}
      setIsAccordionOpen={setIsAccordionOpen}
      accordionKey="selectedDate">
      
      {/* Date Selection Tabs */}
      <div className="mb-4 flex rounded-lg bg-gray-50 p-1">
        <button
          type="button"
          onClick={() => setActiveTab('checkin')}
          className={cn(
            'flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all',
            activeTab === 'checkin'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900',
            // Highlight checkin tab when user needs to select arrival date
            (!date?.from && date?.to) || (!date?.from && !date?.to) ? 'animate-pulse border-2 border-primary-300' : ''
          )}
        >
          {t('arrival_date')}
        </button>
        <button
          type="button"
          onClick={() => setActiveTab('checkout')}
          className={cn(
            'flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all',
            activeTab === 'checkout'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900',
            // Highlight checkout tab when user needs to select departure date
            date?.from && !date?.to && 'animate-pulse border-2 border-primary-300'
          )}
        >
          {t('departure_date')}
        </button>
      </div>

      {/* Calendar */}
      <UiCalendar
        defaultMonth={date?.from}
        disabled={disabledDays}
        mode="range"
        numberOfMonths={1}
        onSelect={handleDateSelection}
        selected={date}
      />
      
      {/* Selected Date Display */}
      {date && (
        <div className="mt-4">
          <ColorfulBadge text={t('check_in_and_delivery_date')} />
          <p className="mt-4 text-lg text-gray-500">
            {t('selected_date_between', {
              from: parseDateToString(date?.from as Date),
              to: parseDateToString(date?.to as Date),
            })}
          </p>
        </div>
      )}
    </SearchAccordionWarper>
  );
};

export default MobileDateTabs;
