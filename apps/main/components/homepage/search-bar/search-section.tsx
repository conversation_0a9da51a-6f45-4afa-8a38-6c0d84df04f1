'use client';
import React, { useEffect, useRef, useState } from 'react';
import type { ReactElement, Dispatch, SetStateAction, FC } from 'react';
import type { DateRange } from 'react-day-picker';
import { usePathname, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Calendar as UiCalendar } from '@/components/ui/calendar';
import SearchTriggerButton from '@/components/homepage/search-bar/search-trigger-button';
import { cn, parseDateToString } from '@/lib/utils';
import GuestsDropdown from '@/components/homepage/search-bar/guests-dropdown';
import { Separator } from '@/components/ui/separator';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import { getIsOneUnitClass } from '@/queries/homepage';
import { useAddQueryParams } from '@/hooks/use-query-params';
import useWindowSizeQuery from '@/hooks/use-window-size-query';
import SearchDrawer from './search-drawer';
import SearchTriggerWrapper from './search-trigger-wrapper';
import BookNowDialog from './book-now-dialog';
import { Classification } from '@/types/homepage';
import useHomePageData from '@/hooks/use-home-page-data';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';
import { isSameDay } from 'date-fns';

type SearchSectionType = {
  date: DateRange | undefined;
  setDate: Dispatch<SetStateAction<DateRange | undefined>>;
  guests: number | undefined;
  setGuests: Dispatch<SetStateAction<number | undefined>>;
  className?: string;
};

const disabledDays = [
  {
    from: new Date(1970, 1, 1),
    to: new Date(new Date().setDate(new Date().getDate() - 1)),
  },
];

const SearchSection: FC<SearchSectionType> = ({ className = '', date, setDate, guests, setGuests }): ReactElement => {
  const locale = useCurrentLocale();
  const isMobile = useWindowSizeQuery('sm');
  const isMD = useWindowSizeQuery('md');

  const t = useScopedI18n('homepage.search');
  const [openDialog, setOpenDialog] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const createQueryParams = useAddQueryParams();
  const { sendGAEvent } = useGAEvent();

  const wrapperRef = useRef<HTMLDivElement | null>(null);

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [arrivalSelected, setArrivalSelected] = useState(false);
  const [departureSelected, setDepartureSelected] = useState(false);
  const [dateSelected, setDateSelected] = useState(false);
  const [guestsSelected, setGuestsSelected] = useState(false);
  const [shouldHighlightDeparture, setShouldHighlightDeparture] = useState(false);
  const [shouldHighlightArrival, setShouldHighlightArrival] = useState(false);

  const { data } = useHomePageData();
  const isOneUnitClass = getIsOneUnitClass(data);

  const handleSearchClick = (): void => {
    if (date?.from && date.to) {
      sendGAEvent({ action: 'search_initiated' });

      const qParams = createQueryParams([
        { name: 'checkin', value: parseDateToString(date.from) },
        { name: 'checkout', value: parseDateToString(date.to) },
        { name: 'guests', value: guests || '1' },
      ]);

      if (isMobile) {
        setIsDrawerOpen(false);
        setDateSelected(false);
        setGuestsSelected(false);
      }

      if (isOneUnitClass) {
        router.push(`${pathname}?${qParams}`);
        setOpenDialog(true);
      } else {
        router.push(`${pathname}/search?${qParams}`);
      }
    } else toast.warning(t('required_search'));
  };

  const handleArrivalClick = () => {
    if (isMobile) {
      setIsDrawerOpen(true);
      setDateSelected(true);
    } else {
      setArrivalSelected(true);
      setDepartureSelected(false);
      setGuestsSelected(false);
    }
  };

  const handleDepartureClick = () => {
    if (isMobile) {
      setIsDrawerOpen(true);
      setDateSelected(true);
    } else {
      setDepartureSelected(true);
      setArrivalSelected(false);
      setGuestsSelected(false);
    }
  };

  const handleGuestsClick = () => {
    setDepartureSelected(false);
    setArrivalSelected(false);
    if (isMobile) {
      setIsDrawerOpen(true);
      setGuestsSelected(true);
    }
  };

  // Close dropdowns only after selecting valid dates
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setArrivalSelected(false);
        setDepartureSelected(false);
        setGuestsSelected(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [arrivalSelected, departureSelected, guestsSelected, isDrawerOpen, dateSelected]);

  // Handle auto reset when only one day is selected and manage highlighting
  useEffect(() => {
    if (!date?.from && !date?.to) {
      setDepartureSelected(false);
      setArrivalSelected(false);
      setShouldHighlightDeparture(false);
    }
    if (date?.from && date?.to && isSameDay(date.from, date.to)) {
      setDepartureSelected(true);
      setArrivalSelected(false);
      setShouldHighlightDeparture(true);

      setDate((prev) => {
        if (prev?.to === undefined) return prev;
        return { from: prev.from, to: undefined };
      });
    }
    if (date?.from && date?.to && !isSameDay(date.from, date.to)) {
      setDepartureSelected(false);
      setArrivalSelected(false);
      setShouldHighlightDeparture(false);
    }
    // Highlight departure when we have check-in but no check-out
    if (date?.from && !date?.to) {
      setShouldHighlightDeparture(true);
    }
  }, [date?.from, date?.to]);

  const handleDateSelection = (range: DateRange | undefined) => {
    if (!range) {
      setDate(range);
      setShouldHighlightDeparture(false);
      return;
    }

    // Prevent invalid date selections
    if (range.from && range.to && range.from > range.to) {
      return; // Don't allow invalid date ranges
    }

    // If arrival tab is selected, update check-in date only
    if (arrivalSelected) {
      if (range.from) {
        // Validate: if there's an existing checkout date and new checkin is after it,
        // treat the new selection as checkout instead
        if (date?.to && range.from > date.to) {
          setDate({ from: date.from, to: range.from });
          setArrivalSelected(false);
          setDepartureSelected(false);
          setShouldHighlightDeparture(false);
        } else {
          // Update check-in date only, preserve existing checkout
          setDate({ from: range.from, to: date?.to });
          // If we don't have checkout date, highlight departure for selection
          if (!date?.to) {
            setShouldHighlightDeparture(true);
            setArrivalSelected(false);
          } else {
            setArrivalSelected(false);
            setShouldHighlightDeparture(false);
          }
        }
      }
    }
    // If departure tab is selected, update check-out date only
    else if (departureSelected) {
      if (range.from) {
        // Validate: if there's an existing checkin date and new date is before it,
        // treat the new selection as checkin instead
        if (date?.from && range.from < date.from) {
          setDate({ from: range.from, to: date.from });
          setDepartureSelected(false);
          setArrivalSelected(false);
          setShouldHighlightDeparture(false);
        } else {
          // Update check-out date only, preserve existing checkin
          setDate({ from: date?.from, to: range.from });
          setDepartureSelected(false);
          setArrivalSelected(false);
          setShouldHighlightDeparture(false);
        }
      }
    }
    // Default range selection behavior when no specific tab is selected
    else {
      // Only allow this when neither tab is specifically selected
      setDate(range);
      setShouldHighlightDeparture(false);
    }
  };

  const renderCalendar = (): ReactElement => (
    <UiCalendar
      defaultMonth={date?.from}
      disabled={disabledDays}
      mode="range"
      numberOfMonths={isMD ? 1 : 2}
      selected={date}
      onSelect={handleDateSelection}
    />
  );

  return (
    <>
      <div
        ref={wrapperRef}
        className={cn(
          'md:shadow-3xl shadow-xs bg-white-50 z-10 flex max-h-20 items-center gap-2',
          'h-12 rounded-full md:h-20',
          className
        )}>
        <div className="flex flex-1">
          <SearchTriggerWrapper
            showPopover={!isMobile}
            open={arrivalSelected}
            setOpen={setArrivalSelected}
            popoverContent={renderCalendar()}>
            <SearchTriggerButton
              onClick={handleArrivalClick}
              mobileText={t('arrival')}
              bottomParagraph={date?.from === undefined ? t('add_date') : date.from.toLocaleDateString(locale)}
              icon={<IconWrapper name="ClendarIcon" size={24} variant="Linear" />}
              topParagraph={t('arrival_date')}
              highlighted={shouldHighlightArrival}
            />
          </SearchTriggerWrapper>

          <Separator className="bg-gray-75" orientation="vertical" />

          <SearchTriggerWrapper
            showPopover={!isMobile}
            open={departureSelected}
            setOpen={setDepartureSelected}
            popoverContent={renderCalendar()}>
            <SearchTriggerButton
              onClick={handleDepartureClick}
              mobileText={t('departure')}
              bottomParagraph={date?.to === undefined ? t('add_date') : date.to.toLocaleDateString(locale)}
              icon={<IconWrapper name="ClendarIcon" size={24} variant="Linear" />}
              topParagraph={t('departure_date')}
              highlighted={shouldHighlightDeparture}
            />
          </SearchTriggerWrapper>

          <Separator className="bg-gray-75" orientation="vertical" />

          <SearchTriggerWrapper
            showPopover={!isMobile}
            open={guestsSelected}
            setOpen={setGuestsSelected}
            popoverContent={<GuestsDropdown guests={guests} setGuests={setGuests} />}>
            <SearchTriggerButton
              onClick={handleGuestsClick}
              mobileText={t('guests')}
              bottomParagraph={guests === undefined ? t('add_guests') : guests.toString()}
              icon={<IconWrapper name="GuestIcon" size={24} />}
              topParagraph={t('number_guests')}
            />
          </SearchTriggerWrapper>
        </div>

        <div className="flex h-full flex-none items-center justify-center gap-2 p-1 !ps-0 md:p-2">
          <Button
            onClick={handleSearchClick}
            className="flex h-full w-[50px] items-center justify-center gap-2 md:w-[115px] md:px-4 md:py-3 ltr:rounded-r-full rtl:rounded-l-full"
            type="button">
            {!isOneUnitClass ? (
              <IconWrapper className="text-white-50" name="SearchIcon" size={24} variant="TwoTone" />
            ) : (
              <IconWrapper name="Tick" className="text-white-50 md:hidden" size={24} variant="TwoTone" />
            )}
            <p className="lg-text text-white-50 hidden text-center font-bold md:flex">
              {isOneUnitClass ? t('book') : t('search')}
            </p>
          </Button>
        </div>
      </div>

      {isMobile && (
        <SearchDrawer
          guestsSelected={guestsSelected}
          open={isDrawerOpen}
          setOpen={setIsDrawerOpen}
          setDate={setDate}
          date={date as DateRange}
          dateSelected={dateSelected}
          setDateSelected={setDateSelected}
          setGuestsSelected={setGuestsSelected}
          guestCount={guests as number}
          setGuestCount={setGuests}
          title={isOneUnitClass ? t('book') : t('search')}
          handleSubmit={handleSearchClick}
        />
      )}

      <BookNowDialog
        isBottomSheet={isMobile}
        classifications={data?.classifications as Classification}
        open={openDialog}
        setOpen={setOpenDialog}
      />
    </>
  );
};

export default SearchSection;
