import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Drawer, DrawerClose, DrawerContent, DrawerFooter, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { FC, Dispatch, SetStateAction } from 'react';
import MobileDateTabs from './mobile-date-tabs';
import { DateRange } from 'react-day-picker';
import GustsCountAccordion from './guests-accordion';

interface SearchDrawerProps {
  open: boolean;
  setOpen: (isOpen: boolean) => void;
  setDate: (date: DateRange) => void;
  dateSelected: boolean;
  date: DateRange;
  setDateSelected: (isSelected: boolean) => void;
  guestsSelected: boolean;
  setGuestsSelected: (isSelected: boolean) => void;
  guestCount: number;
  setGuestCount: Dispatch<SetStateAction<number | undefined>>;
  title: string;
  handleSubmit: () => void;
}
const SearchDrawer: FC<SearchDrawerProps> = ({
  guestsSelected,
  open,
  setOpen,
  setDate,
  date,
  dateSelected,
  setDateSelected,
  setGuestsSelected,
  guestCount,
  setGuestCount,
  title,
  handleSubmit,
}) => {
  return (
    <Drawer open={open} onOpenChange={setOpen} direction="bottom">
      <DrawerContent isBottom className="max-h-[90vh]">
        <DrawerHeader className="px-6 py-3">
          <DrawerTitle>{title}</DrawerTitle>
          <DrawerClose onClick={() => setOpen(false)} asChild>
            <Button variant="ghost" className="hover:text-secondary-400 p-0">
              <IconWrapper name="Close" className="text-secondary" size={28} />
            </Button>
          </DrawerClose>
        </DrawerHeader>
        <Separator className="bg-gray-75 h-[0.6px]" orientation="horizontal" />
        <div className="scrollable-content flex flex-1 flex-col gap-y-4 overflow-auto">
          <div className="px-3 pt-4 sm:px-6">
            <MobileDateTabs
              setDate={setDate}
              date={date as DateRange}
              isAccordionOpen={dateSelected}
              setIsAccordionOpen={setDateSelected}
            />
            <GustsCountAccordion
              isAccordionOpen={guestsSelected}
              setIsAccordionOpen={setGuestsSelected}
              setGuestCount={setGuestCount}
              guestCount={guestCount}
            />
          </div>
        </div>
        <DrawerFooter isBottom>
          <Button variant="default" size="lg" className="text-white-full w-full" onClick={handleSubmit}>
            {title}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default SearchDrawer;
